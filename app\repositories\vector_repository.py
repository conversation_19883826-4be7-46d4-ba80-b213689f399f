"""
向量操作Repository

负责：
- 向量插入和批量插入
- 向量更新和Upsert操作
- 向量删除操作
- 混合检索向量插入
- 向量记录构建
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from app.core.vectordb import VectorDatabase
from app.utils.data_utils import RecordBuilder


class VectorRepository:
    """向量操作Repository"""
    
    def __init__(self, db: VectorDatabase):
        """
        初始化向量操作Repository
        
        Args:
            db: 数据库连接实例
        """
        self.db = db
        self.record_builder = RecordBuilder()
    
    async def insert_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]]
    ) -> int:
        """
        插入向量记录
        
        Args:
            collection_name: 集合名称
            records: 向量记录列表
            
        Returns:
            int: 插入的记录数
            
        Raises:
            Exception: 插入失败
        """
        print(f"[向量] 插入向量到集合 '{collection_name}'，记录数: {len(records)}")
        
        try:
            if hasattr(self.db, 'insert_vectors'):
                insert_method = getattr(self.db, 'insert_vectors')
                result = await insert_method(collection_name, records)
                print(f"[成功] 向量插入完成，插入记录数: {result if isinstance(result, int) else len(records)}")
                return result if isinstance(result, int) else len(records)
            else:
                raise Exception("数据库不支持向量插入操作")
        except Exception as e:
            print(f"[错误] 插入向量失败: {e}")
            raise Exception(f"插入向量失败: {str(e)}")
    
    async def insert_hybrid_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]]
    ) -> int:
        """
        插入混合检索向量记录
        
        Args:
            collection_name: 集合名称
            records: 混合检索向量记录列表
            
        Returns:
            int: 插入的记录数
            
        Raises:
            Exception: 插入失败
        """
        print(f"[混合向量] 插入混合检索向量到集合 '{collection_name}'，记录数: {len(records)}")
        
        try:
            if hasattr(self.db, 'insert_hybrid_vectors'):
                insert_method = getattr(self.db, 'insert_hybrid_vectors')
                result = await insert_method(collection_name, records)
                print(f"[成功] 混合检索向量插入完成，插入记录数: {result if isinstance(result, int) else len(records)}")
                return result if isinstance(result, int) else len(records)
            else:
                raise Exception("数据库不支持混合检索向量插入操作")
        except Exception as e:
            print(f"[错误] 插入混合检索向量失败: {e}")
            raise Exception(f"插入混合检索向量失败: {str(e)}")
    
    async def upsert_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]]
    ) -> int:
        """
        更新或插入向量记录
        
        Args:
            collection_name: 集合名称
            records: 向量记录列表
            
        Returns:
            int: 处理的记录数
            
        Raises:
            Exception: Upsert失败
        """
        print(f"[向量] Upsert向量到集合 '{collection_name}'，记录数: {len(records)}")
        
        try:
            if hasattr(self.db, 'upsert_vectors'):
                upsert_method = getattr(self.db, 'upsert_vectors')
                result = await upsert_method(collection_name, records)
                print(f"[成功] 向量Upsert完成，处理记录数: {result if isinstance(result, int) else len(records)}")
                return result if isinstance(result, int) else len(records)
            else:
                raise Exception("数据库不支持向量Upsert操作")
        except Exception as e:
            print(f"[错误] Upsert向量失败: {e}")
            raise Exception(f"Upsert向量失败: {str(e)}")
    
    async def delete_vectors(
        self,
        collection_name: str,
        ids: Optional[List[Any]] = None,
        filter_expr: str = ""
    ) -> int:
        """
        删除向量记录
        
        Args:
            collection_name: 集合名称
            ids: 要删除的ID列表
            filter_expr: 删除条件表达式
            
        Returns:
            int: 删除的记录数
            
        Raises:
            Exception: 删除失败
        """
        if ids:
            print(f"[向量] 按ID删除向量，集合: '{collection_name}'，ID数量: {len(ids)}")
        else:
            print(f"[向量] 按条件删除向量，集合: '{collection_name}'，条件: {filter_expr}")
        
        try:
            if hasattr(self.db, 'delete_vectors'):
                delete_method = getattr(self.db, 'delete_vectors')
                result = await delete_method(collection_name, ids=ids, filter_expr=filter_expr)
                print(f"[成功] 向量删除完成，删除记录数: {result if isinstance(result, int) else 0}")
                return result if isinstance(result, int) else 0
            else:
                raise Exception("数据库不支持向量删除操作")
        except Exception as e:
            print(f"[错误] 删除向量失败: {e}")
            raise Exception(f"删除向量失败: {str(e)}")
    
    async def batch_insert_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]],
        batch_size: int = 10
    ) -> int:
        """
        批量插入向量记录
        
        Args:
            collection_name: 集合名称
            records: 向量记录列表
            batch_size: 批次大小
            
        Returns:
            int: 插入的记录数
            
        Raises:
            Exception: 批量插入失败
        """
        print(f"[向量] 批量插入向量，集合: '{collection_name}'，总记录数: {len(records)}，批次大小: {batch_size}")
        
        total_inserted = 0
        
        try:
            # 将记录分成批次
            batches = []
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batches.append(batch)
            
            print(f"[向量] 分为 {len(batches)} 个批次进行插入")
            
            # 按批次插入
            for i, batch in enumerate(batches):
                print(f"[向量] 处理批次 {i + 1}/{len(batches)}，记录数: {len(batch)}")
                
                batch_result = await self.insert_vectors(collection_name, batch)
                total_inserted += batch_result
                
                print(f"[向量] 批次 {i + 1} 插入完成，本批次插入: {batch_result}")
            
            print(f"[成功] 批量插入完成，总插入记录数: {total_inserted}")
            return total_inserted
            
        except Exception as e:
            print(f"[错误] 批量插入向量失败: {e}")
            raise Exception(f"批量插入向量失败: {str(e)}")
    
    async def batch_insert_hybrid_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]],
        batch_size: int = 10
    ) -> int:
        """
        批量插入混合检索向量记录
        
        Args:
            collection_name: 集合名称
            records: 混合检索向量记录列表
            batch_size: 批次大小
            
        Returns:
            int: 插入的记录数
            
        Raises:
            Exception: 批量插入失败
        """
        print(f"[混合向量] 批量插入混合检索向量，集合: '{collection_name}'，总记录数: {len(records)}，批次大小: {batch_size}")
        
        total_inserted = 0
        
        try:
            # 将记录分成批次
            batches = []
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batches.append(batch)
            
            print(f"[混合向量] 分为 {len(batches)} 个批次进行插入")
            
            # 按批次插入
            for i, batch in enumerate(batches):
                print(f"[混合向量] 处理批次 {i + 1}/{len(batches)}，记录数: {len(batch)}")
                
                batch_result = await self.insert_hybrid_vectors(collection_name, batch)
                total_inserted += batch_result
                
                print(f"[混合向量] 批次 {i + 1} 插入完成，本批次插入: {batch_result}")
            
            print(f"[成功] 混合检索向量批量插入完成，总插入记录数: {total_inserted}")
            return total_inserted
            
        except Exception as e:
            print(f"[错误] 批量插入混合检索向量失败: {e}")
            raise Exception(f"批量插入混合检索向量失败: {str(e)}")
    
    async def concurrent_insert_vectors(
        self,
        collection_name: str,
        records: List[Dict[str, Any]],
        batch_size: int = 10,
        max_concurrent: int = 3
    ) -> int:
        """
        并发插入向量记录
        
        Args:
            collection_name: 集合名称
            records: 向量记录列表
            batch_size: 批次大小
            max_concurrent: 最大并发数
            
        Returns:
            int: 插入的记录数
            
        Raises:
            Exception: 并发插入失败
        """
        print(f"[向量] 并发插入向量，集合: '{collection_name}'，总记录数: {len(records)}")
        print(f"[向量] 配置 - 批次大小: {batch_size}，最大并发数: {max_concurrent}")
        
        total_inserted = 0
        
        try:
            # 将记录分成批次
            batches = []
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batches.append(batch)
            
            print(f"[向量] 分为 {len(batches)} 个批次进行并发插入")
            
            # 定义单个批次的处理函数
            async def process_batch(batch_index: int, batch: List[Dict[str, Any]]) -> int:
                print(f"[并发向量] 处理批次 {batch_index + 1}，记录数: {len(batch)}")
                result = await self.insert_vectors(collection_name, batch)
                print(f"[并发向量] 批次 {batch_index + 1} 完成，插入记录数: {result}")
                return result
            
            # 并发处理批次
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def limited_process_batch(batch_index: int, batch: List[Dict[str, Any]]) -> int:
                async with semaphore:
                    return await process_batch(batch_index, batch)
            
            # 创建并发任务
            tasks = [
                limited_process_batch(i, batch)
                for i, batch in enumerate(batches)
            ]
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks)
            
            # 统计总插入数
            total_inserted = sum(results)
            
            print(f"[成功] 并发插入完成，总插入记录数: {total_inserted}")
            return total_inserted
            
        except Exception as e:
            print(f"[错误] 并发插入向量失败: {e}")
            raise Exception(f"并发插入向量失败: {str(e)}")
    
    def build_normal_vector_record(
        self,
        content: str,
        vector: List[float],
        metadata: Optional[Dict[str, Any]] = None,
        record_id: Optional[Union[int, str]] = None,
        **extra_fields
    ) -> Dict[str, Any]:
        """
        构建普通向量记录
        
        Args:
            content: 文本内容
            vector: 向量数据
            metadata: 元数据
            record_id: 记录ID
            **extra_fields: 额外字段
            
        Returns:
            Dict[str, Any]: 向量记录
        """
        return self.record_builder.build_vector_record(
            content=content,
            vector=vector,
            metadata=metadata or {},
            record_id=record_id,
            **extra_fields
        )
    

    
    async def count_vectors(self, collection_name: str) -> int:
        """
        统计集合中的向量数量
        
        Args:
            collection_name: 集合名称
            
        Returns:
            int: 向量数量
            
        Raises:
            Exception: 统计失败
        """
        try:
            if hasattr(self.db, 'count_vectors'):
                count_method = getattr(self.db, 'count_vectors')
                count = await count_method(collection_name)
                print(f"[向量] 集合 '{collection_name}' 中的向量数量: {count}")
                return count
            else:
                raise Exception("数据库不支持向量计数操作")
        except Exception as e:
            print(f"[错误] 统计向量数量失败: {e}")
            raise Exception(f"统计向量数量失败: {str(e)}")
    
    async def get_vector_by_id(
        self, 
        collection_name: str, 
        vector_id: Union[int, str]
    ) -> Optional[Dict[str, Any]]:
        """
        根据ID获取向量记录
        
        Args:
            collection_name: 集合名称
            vector_id: 向量ID
            
        Returns:
            Optional[Dict[str, Any]]: 向量记录，如果不存在返回None
            
        Raises:
            Exception: 获取失败
        """
        try:
            if hasattr(self.db, 'get_vector_by_id'):
                get_method = getattr(self.db, 'get_vector_by_id')
                result = await get_method(collection_name, vector_id)
                return result
            else:
                # 通过查询实现
                from app.repositories.search_repository import SearchRepository
                search_repo = SearchRepository(self.db)
                filter_expr = f"id == {vector_id}"
                results = await search_repo.search_vectors(
                    collection_name=collection_name,
                    query_vector=[0.0] * 768,  # 占位向量
                    top_k=1,
                    filter_expr=filter_expr
                )
                return results[0] if results else None
        except Exception as e:
            print(f"[错误] 获取向量记录失败: {e}")
            return None
    
    async def update_vector_metadata(
        self,
        collection_name: str,
        vector_id: Union[int, str],
        new_metadata: Dict[str, Any]
    ) -> bool:
        """
        更新向量的元数据
        
        Args:
            collection_name: 集合名称
            vector_id: 向量ID
            new_metadata: 新的元数据
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            Exception: 更新失败
        """
        try:
            # 先获取现有记录
            existing_record = await self.get_vector_by_id(collection_name, vector_id)
            if not existing_record:
                raise Exception(f"向量ID {vector_id} 不存在")
            
            # 更新元数据
            existing_record['metadata'].update(new_metadata)
            
            # 执行upsert
            result = await self.upsert_vectors(collection_name, [existing_record])
            return result > 0
            
        except Exception as e:
            print(f"[错误] 更新向量元数据失败: {e}")
            raise Exception(f"更新向量元数据失败: {str(e)}")
    
    def get_insert_statistics(self) -> Dict[str, Any]:
        """
        获取插入统计信息
        
        Returns:
            Dict[str, Any]: 插入统计信息
        """
        # 这里可以添加统计逻辑，如插入次数、耗时等
        return {
            "total_inserts": 0,  # 需要在实际插入时累加
            "total_records": 0,
            "average_batch_size": 0,
            "last_insert_time": None
        } 