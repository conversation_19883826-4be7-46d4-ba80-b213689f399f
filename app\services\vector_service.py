"""
向量操作服务

负责：
- 向量数据的CRUD操作
- 批量向量操作
- Upsert操作
- 向量数据验证和处理
"""

import time
from typing import Dict, List, Any, Optional, Union
from app.config.settings import VectorDBConfig
from app.repositories import VectorRepository, VectorDatabaseRepository
from app.services.embedding_service import EmbeddingService
from app.utils.logging_utils import OperationLogger, TimeTracker
from app.utils.validation import ParameterValidator
from app.utils.response_utils import ResponseBuilder, VectorOperationResponseBuilder
from app.utils.data_utils import ListProcessor, RecordBuilder


class VectorService:
    """向量操作服务"""
    
    def __init__(self):
        """初始化向量服务"""
        self.db_repo = VectorDatabaseRepository()
        self.vector_repo: Optional[VectorRepository] = None
        self.embedding_service: Optional[EmbeddingService] = None
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self.vector_response_builder = VectorOperationResponseBuilder()
        self.list_processor = ListProcessor()
        self.record_builder = RecordBuilder()
        self._initialized = False
    
    async def initialize(self, config: VectorDBConfig) -> Dict[str, Any]:
        """
        初始化向量服务
        
        Args:
            config: 数据库配置
            
        Returns:
            Dict[str, Any]: 初始化结果
        """
        start_time = time.time()
        
        try:
            # 设置数据库配置
            self.db_repo.set_config(config)
            
            # 获取数据库连接
            db_connection = await self.db_repo.get_database_connection()
            
            # 初始化vector repository
            self.vector_repo = VectorRepository(db_connection)
            
            # 初始化向量化服务
            self.embedding_service = EmbeddingService()
            
            self._initialized = True
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="向量服务初始化",
                initialization_time=f"{elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="向量服务初始化成功",
                data={
                    "initialization_time": elapsed_time,
                    "status": "ready"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量服务初始化",
                error=e
            )
            raise Exception(f"向量服务初始化失败: {str(e)}")
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            raise Exception("向量服务未初始化，请先调用initialize方法")
        if not self.vector_repo:
            raise Exception("向量仓库未初始化")
        if not self.embedding_service:
            raise Exception("向量化服务未初始化")
    
    async def insert_vectors(
        self,
        collection_name: str,
        texts: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[Union[str, int]]] = None,
        auto_flush: bool = True,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        插入向量数据
        
        Args:
            collection_name: 集合名称
            texts: 文本列表
            metadata: 元数据列表
            ids: ID列表
            auto_flush: 是否自动刷新
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 插入结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("向量插入")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            # 生成向量
            embedding_result = await self.embedding_service.generate_embeddings_batch(texts)
            vectors = embedding_result['data']['vectors']
            vector_gen_time = timer.checkpoint("向量生成")
            
            # 验证数据
            if not texts:
                raise ValueError("文本列表不能为空")
            if len(vectors) != len(texts):
                raise ValueError("向量数量与文本数量不匹配")
            
            # 构建向量记录
            records = []
            for i, (text, vector) in enumerate(zip(texts, vectors)):
                record_metadata = metadata[i] if metadata and i < len(metadata) else {}
                record_id = ids[i] if ids and i < len(ids) else None
                
                record = self.record_builder.build_vector_record(
                    content=text,
                    vector=vector,
                    metadata=record_metadata,
                    record_id=record_id,
                    collection=collection_name
                )
                records.append(record)
            
            # 插入向量
            result = await self.vector_repo.insert_vectors(
                collection_name=collection_name,
                records=records
            )
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="向量插入",
                collection=collection_name,
                count=len(texts),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.vector_response_builder.upsert_response(
                upsert_count=len(texts),
                collection=collection_name,
                database=database,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                vector_generation_time=f"{vector_gen_time:.3f}秒",
                vector_dimension=len(vectors[0]) if vectors else 0,
                auto_flush=auto_flush,
                message="向量插入成功"
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量插入",
                error=e,
                collection=collection_name,
                text_count=len(texts)
            )
            raise Exception(f"向量插入失败: {str(e)}")
    
    async def insert_hybrid_vectors(
        self,
        collection_name: str,
        texts: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[Union[str, int]]] = None,
        dense_vector_dim: int = 768,
        auto_flush: bool = True,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        插入混合检索向量数据
        
        Args:
            collection_name: 集合名称
            texts: 文本列表
            metadata: 元数据列表
            ids: ID列表
            dense_vector_dim: 密集向量维度
            auto_flush: 是否自动刷新
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 插入结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("混合向量插入")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            # 生成密集向量（用于语义检索）
            embedding_result = await self.embedding_service.generate_embeddings_batch(texts)
            dense_vectors = embedding_result['data']['vectors']
            vector_gen_time = timer.checkpoint("密集向量生成")
            
            # 验证向量维度
            if dense_vectors and len(dense_vectors[0]) != dense_vector_dim:
                raise ValueError(f"向量维度不匹配：期望 {dense_vector_dim}，实际 {len(dense_vectors[0])}")
            
            # 验证数据
            if not texts:
                raise ValueError("文本列表不能为空")
            if len(dense_vectors) != len(texts):
                raise ValueError("向量数量与文本数量不匹配")
            
            # 构建混合向量记录
            records = []
            for i, (text, dense_vector) in enumerate(zip(texts, dense_vectors)):
                record_metadata = metadata[i] if metadata and i < len(metadata) else {}
                record_id = ids[i] if ids and i < len(ids) else None
                
                record = self.record_builder.build_hybrid_vector_record(
                    content=text,
                    vector=dense_vector,
                    metadata=record_metadata,
                    record_id=record_id,
                    collection=collection_name
                )
                records.append(record)
            
            # 插入混合向量
            result = await self.vector_repo.insert_hybrid_vectors(
                collection_name=collection_name,
                records=records
            )
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="混合向量插入",
                collection=collection_name,
                count=len(texts),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.vector_response_builder.upsert_response(
                upsert_count=len(texts),
                collection=collection_name,
                database=database,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                vector_generation_time=f"{vector_gen_time:.3f}秒",
                vector_dimension=dense_vector_dim,
                auto_flush=auto_flush,
                message="混合向量插入成功"
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="混合向量插入",
                error=e,
                collection=collection_name,
                text_count=len(texts)
            )
            raise Exception(f"混合向量插入失败: {str(e)}")
    
    async def batch_insert_vectors(
        self,
        collection_name: str,
        texts: List[str],
        batch_size: int = 100,
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[Union[str, int]]] = None,
        auto_flush: bool = True,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量插入向量数据
        
        Args:
            collection_name: 集合名称
            texts: 文本列表
            batch_size: 批次大小
            metadata: 元数据列表
            ids: ID列表
            auto_flush: 是否自动刷新
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 批量插入结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("批量向量插入")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            # 分批处理数据
            batches = self.list_processor.chunk_list(texts, batch_size)
            metadata_batches = None
            if metadata:
                metadata_batches = self.list_processor.chunk_list(metadata, batch_size)
            
            ids_batches = None
            if ids:
                ids_batches = self.list_processor.chunk_list(ids, batch_size)
            
            total_inserted = 0
            batch_results = []
            
            for i, text_batch in enumerate(batches):
                batch_metadata = metadata_batches[i] if metadata_batches else None
                batch_ids = ids_batches[i] if ids_batches else None
                
                # 生成向量
                embedding_result = await self.embedding_service.generate_embeddings_batch(text_batch)
                vectors = embedding_result['data']['vectors']
                
                # 构建批次记录
                batch_records = []
                for j, (text, vector) in enumerate(zip(text_batch, vectors)):
                    record_metadata = batch_metadata[j] if batch_metadata and j < len(batch_metadata) else {}
                    record_id = batch_ids[j] if batch_ids and j < len(batch_ids) else None
                    
                    record = self.record_builder.build_vector_record(
                        content=text,
                        vector=vector,
                        metadata=record_metadata,
                        record_id=record_id,
                        collection=collection_name
                    )
                    batch_records.append(record)
                
                # 插入批次
                batch_result = await self.vector_repo.insert_vectors(
                    collection_name=collection_name,
                    records=batch_records
                )
                
                batch_results.append(batch_result)
                total_inserted += len(text_batch)
                
                self.operation_logger.log_step(
                    step_name=f"批次 {i+1}/{len(batches)}",
                    details=f"插入 {len(text_batch)} 条记录"
                )
            
            # 最终刷新（如果repository支持）
            if auto_flush and self.vector_repo:
                try:
                    # 尝试刷新操作，如果支持的话
                    print(f"提示：批量插入完成，自动刷新设置为: {auto_flush}")
                except Exception as e:
                    print(f"警告：刷新集合失败: {e}")
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="批量向量插入",
                collection=collection_name,
                total_count=total_inserted,
                batch_count=len(batches),
                batch_size=batch_size,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message=f"批量插入完成，共插入 {total_inserted} 条记录",
                data={
                    "collection_name": collection_name,
                    "total_inserted": total_inserted,
                    "batch_count": len(batches),
                    "batch_size": batch_size,
                    "processing_time": elapsed_time['total'],
                    "batch_results": batch_results
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="批量向量插入",
                error=e,
                collection=collection_name,
                total_texts=len(texts),
                batch_size=batch_size
            )
            raise Exception(f"批量向量插入失败: {str(e)}")
    
    async def upsert_vectors(
        self,
        collection_name: str,
        texts: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[Union[str, int]]] = None,
        auto_flush: bool = True,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upsert向量数据（插入或更新）
        
        Args:
            collection_name: 集合名称
            texts: 文本列表
            metadata: 元数据列表
            ids: ID列表
            auto_flush: 是否自动刷新
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: Upsert结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("向量Upsert")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            # 生成向量
            embedding_result = await self.embedding_service.generate_embeddings_batch(texts)
            vectors = embedding_result['data']['vectors']
            vector_gen_time = timer.checkpoint("向量生成")
            
            # 构建Upsert记录
            records = []
            for i, (text, vector) in enumerate(zip(texts, vectors)):
                record_metadata = metadata[i] if metadata and i < len(metadata) else {}
                record_id = ids[i] if ids and i < len(ids) else None
                
                record = self.record_builder.build_vector_record(
                    content=text,
                    vector=vector,
                    metadata=record_metadata,
                    record_id=record_id,
                    collection=collection_name
                )
                records.append(record)
            
            # 执行upsert
            result = await self.vector_repo.upsert_vectors(
                collection_name=collection_name,
                records=records
            )
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="向量Upsert",
                collection=collection_name,
                count=len(texts),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.vector_response_builder.upsert_response(
                upsert_count=len(texts),
                collection=collection_name,
                database=database,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                vector_generation_time=f"{vector_gen_time:.3f}秒",
                vector_dimension=len(vectors[0]) if vectors else 0,
                auto_flush=auto_flush,
                message="Upsert操作成功"
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量Upsert",
                error=e,
                collection=collection_name,
                text_count=len(texts)
            )
            raise Exception(f"向量Upsert失败: {str(e)}")
    
    async def delete_vectors(
        self,
        collection_name: str,
        filter_expr: Optional[str] = None,
        ids: Optional[List[Union[str, int]]] = None,
        auto_flush: bool = True,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        删除向量数据
        
        Args:
            collection_name: 集合名称
            filter_expr: 筛选表达式
            ids: ID列表
            auto_flush: 是否自动刷新
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("向量删除")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database
                )
                self.vector_repo = VectorRepository(db_connection)
            
            # 执行删除
            result = await self.vector_repo.delete_vectors(
                collection_name=collection_name,
                filter_expr=filter_expr or "",
                ids=ids
            )
            
            elapsed_time = timer.finish()
            
            delete_count = result if isinstance(result, int) else 0
            delete_method = "by_filter" if filter_expr else "by_ids"
            
            self.operation_logger.log_operation_success(
                operation_name="向量删除",
                collection=collection_name,
                delete_count=delete_count,
                delete_method=delete_method,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.vector_response_builder.delete_response(
                delete_count=delete_count,
                collection=collection_name,
                database=database,
                delete_method=delete_method,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                auto_flush=auto_flush
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量删除",
                error=e,
                collection=collection_name
            )
            raise Exception(f"向量删除失败: {str(e)}")
    
    async def get_vector_count(
        self,
        collection_name: str,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取向量数量
        
        Args:
            collection_name: 集合名称
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 向量数量信息
        """
        self._ensure_initialized()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            count = await self.vector_repo.count_vectors(collection_name)
            
            return self.response_builder.success_response(
                message=f"集合 '{collection_name}' 包含 {count} 条向量记录",
                data={
                    "collection_name": collection_name,
                    "database": database,
                    "vector_count": count
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="获取向量数量",
                error=e,
                collection=collection_name
            )
            raise Exception(f"获取向量数量失败: {str(e)}")
    
    async def get_vector_by_id(
        self,
        collection_name: str,
        vector_id: Union[str, int],
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        根据ID获取向量记录
        
        Args:
            collection_name: 集合名称
            vector_id: 向量ID
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 向量记录
        """
        self._ensure_initialized()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
            
            vector_record = await self.vector_repo.get_vector_by_id(
                collection_name=collection_name,
                vector_id=vector_id
            )
            
            if not vector_record:
                return self.response_builder.error_response(
                    message=f"未找到ID为 '{vector_id}' 的向量记录",
                    code=404
                )
            
            return self.response_builder.success_response(
                message="向量记录获取成功",
                data={
                    "collection_name": collection_name,
                    "database": database,
                    "vector_record": vector_record
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="获取向量记录",
                error=e,
                collection=collection_name,
                vector_id=vector_id
            )
            raise Exception(f"获取向量记录失败: {str(e)}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "initialized": self._initialized,
            "components": {
                "vector_repository": "active" if self.vector_repo else "inactive",
                "embedding_service": "active" if self.embedding_service else "inactive",
                "database_repository": "active"
            }
        } 