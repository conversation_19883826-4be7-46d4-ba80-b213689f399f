#!/bin/bash

# ========================================================================================
# 脚本说明:
# 优雅地停止由 start.sh 启动的 Gunicorn 服务。
#
# 使用方法:
# 运行 `bash scripts/shutdown.sh`
# ========================================================================================

PID_FILE="gunicorn.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "PID文件 ($PID_FILE) 未找到。服务可能没有在运行。"
else
    echo "🛑 正在停止Gunicorn服务 (PID: $(cat $PID_FILE))..."
    # 从PID文件读取进程ID，并发送TERM信号，Gunicorn会进行优雅关闭
    kill -TERM $(cat $PID_FILE)
    
    # 删除PID文件
    rm $PID_FILE
    
    echo "✅ 服务已停止。"
fi