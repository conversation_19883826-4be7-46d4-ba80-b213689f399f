"""
搜索服务

负责：
- 向量搜索
- 多集合搜索
- 分页搜索
- 搜索结果处理和格式化
"""

import time
from typing import Dict, List, Any, Optional, Union
from app.config.settings import VectorDBConfig
from app.repositories import SearchRepository, VectorDatabaseRepository
from app.services.embedding_service import EmbeddingService
from app.utils.logging_utils import OperationLogger, TimeTracker
from app.utils.validation import ParameterValidator, validate_search_parameters
from app.utils.response_utils import ResponseBuilder, SearchResponseBuilder
from app.utils.filter_utils import FilterExpressionBuilder
from app.utils.formatting import SearchResultFormatter


class SearchService:
    """搜索服务"""
    
    def __init__(self):
        """初始化搜索服务"""
        self.db_repo = VectorDatabaseRepository()
        self.search_repo: Optional[SearchRepository] = None
        self.embedding_service: Optional[EmbeddingService] = None
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self.search_response_builder = SearchResponseBuilder()
        # FilterExpressionBuilder 使用静态方法，不需要实例化
        self._initialized = False
    
    async def initialize(self, config: VectorDBConfig) -> Dict[str, Any]:
        """
        初始化搜索服务
        
        Args:
            config: 数据库配置
            
        Returns:
            Dict[str, Any]: 初始化结果
        """
        start_time = time.time()
        
        try:
            # 设置数据库配置
            self.db_repo.set_config(config)
            
            # 获取数据库连接
            db_connection = await self.db_repo.get_database_connection()
            
            # 初始化search repository
            self.search_repo = SearchRepository(db_connection)
            
            # 初始化向量化服务
            self.embedding_service = EmbeddingService()
            
            self._initialized = True
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="搜索服务初始化",
                initialization_time=f"{elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="搜索服务初始化成功",
                data={
                    "initialization_time": elapsed_time,
                    "status": "ready"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="搜索服务初始化",
                error=e
            )
            raise Exception(f"搜索服务初始化失败: {str(e)}")
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            raise Exception("搜索服务未初始化，请先调用initialize方法")
        if not self.search_repo:
            raise Exception("搜索仓库未初始化")
        if not self.embedding_service:
            raise Exception("向量化服务未初始化")
    
    async def search_vectors(
        self,
        collection_name: str,
        query: str,
        top_k: int = 10,
        filter_expr: Optional[str] = None,
        output_fields: Optional[List[str]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        向量搜索
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("向量搜索")
        timer.start()
        
        try:
            # 参数验证
            top_k, _ = validate_search_parameters(top_k)
            
            # 基础参数验证
            if not collection_name:
                raise ValueError("集合名称不能为空")
            if not query or not query.strip():
                raise ValueError("查询文本不能为空")
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.search_repo = SearchRepository(db_connection)
            
            # 生成查询向量
            embedding_result = await self.embedding_service.generate_embedding(query)
            query_vector = embedding_result['data']['vector']
            vector_gen_time = timer.checkpoint("查询向量生成")
            
            # 执行搜索
            search_results = await self.search_repo.search_vectors(
                collection_name=collection_name,
                query_vector=query_vector,
                top_k=top_k,
                filter_expr=filter_expr or ""
            )
            
            search_time = timer.checkpoint("向量搜索")
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="向量搜索",
                collection=collection_name,
                query_length=len(query),
                results_count=len(formatted_results),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                collection=collection_name,
                database=database,
                time_details={
                    "vector_generation": f"{vector_gen_time:.3f}秒",
                    "search_execution": f"{search_time:.3f}秒"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量搜索",
                error=e,
                collection=collection_name,
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"向量搜索失败: {str(e)}")
    
    async def search_multiple_collections(
        self,
        collections: List[str],
        query: str,
        top_k: int = 10,
        databases: Optional[List[str]] = None,
        filter_expr: Optional[str] = None,
        output_fields: Optional[List[str]] = None,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        多集合搜索
        
        Args:
            collections: 集合名称列表
            query: 查询文本
            top_k: 每个集合返回的结果数量
            databases: 数据库名称列表
            filter_expr: 筛选表达式
            output_fields: 输出字段
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("多集合搜索")
        timer.start()
        
        try:
            # 参数验证
            if not collections:
                raise ValueError("集合列表不能为空")
            
            # 生成查询向量（使用指定的embedding类型）
            embedding_result = await self.embedding_service.generate_embedding(
                query,
                embedding_type=embedding_type
            )
            query_vector = embedding_result['data']['vector']
            vector_gen_time = timer.checkpoint("查询向量生成")
            
            all_results = []
            search_errors = []
            
            # 逐个搜索集合
            for i, collection_name in enumerate(collections):
                try:
                    # 切换数据库
                    if databases and i < len(databases):
                        db_connection = await self.db_repo.get_database_connection(
                            database=databases[i],
                            create_if_not_exists=True
                        )
                        self.search_repo = SearchRepository(db_connection)
                    
                    # 执行搜索
                    collection_results = await self.search_repo.search_vectors(
                        collection_name=collection_name,
                        query_vector=query_vector,
                        top_k=top_k,
                        filter_expr=filter_expr or ""
                    )
                    
                    # 添加集合信息到结果
                    for result in collection_results:
                        result['source_collection'] = collection_name
                        if databases and i < len(databases):
                            result['source_database'] = databases[i]
                    
                    all_results.extend(collection_results)
                    
                except Exception as e:
                    search_errors.append({
                        "collection": collection_name,
                        "database": databases[i] if databases and i < len(databases) else None,
                        "error": str(e)
                    })
            
            # 合并和排序结果
            if all_results:
                all_results = sorted(all_results, key=lambda x: x.get('similarity', 0), reverse=True)
                # 限制总结果数量
                all_results = all_results[:top_k * len(collections)]
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(all_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="多集合搜索",
                collections_count=len(collections),
                results_count=len(formatted_results),
                errors_count=len(search_errors),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.multi_search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                databases_searched=len(set(databases) if databases else [None])
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="多集合搜索",
                error=e,
                collections_count=len(collections),
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"多集合搜索失败: {str(e)}")
    
    async def search_with_pagination(
        self,
        collection_name: str,
        query: str,
        page: int = 1,
        page_size: int = 10,
        filter_expr: Optional[str] = None,
        output_fields: Optional[List[str]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分页搜索
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            page: 页码
            page_size: 每页大小
            filter_expr: 筛选表达式
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 分页搜索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("分页搜索")
        timer.start()
        
        try:
            # 参数验证
            if page < 1:
                raise ValueError("页码必须大于0")
            if page_size < 1:
                raise ValueError("每页大小必须大于0")
            
            # 计算查询参数
            offset = (page - 1) * page_size
            limit = page_size
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.search_repo = SearchRepository(db_connection)
            
            # 生成查询向量
            embedding_result = await self.embedding_service.generate_embedding(query)
            query_vector = embedding_result['data']['vector']
            
            # 执行搜索 (分页功能通过 top_k 实现)
            search_results = await self.search_repo.search_vectors(
                collection_name=collection_name,
                query_vector=query_vector,
                top_k=limit,
                filter_expr=filter_expr or ""
            )
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="分页搜索",
                collection=collection_name,
                page=page,
                page_size=page_size,
                results_count=len(formatted_results),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            # 使用分页响应构建器
            return self.response_builder.paginated_response(
                items=formatted_results,
                total=len(formatted_results),  # 注意：这里只是当前页的数量，实际应用中需要查询总数
                page=page,
                page_size=page_size,
                message="分页搜索完成"
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="分页搜索",
                error=e,
                collection=collection_name,
                page=page,
                page_size=page_size
            )
            raise Exception(f"分页搜索失败: {str(e)}")
    
    async def search_with_filters(
        self,
        collection_name: str,
        query: str,
        filters: Dict[str, Any],
        top_k: int = 10,
        output_fields: Optional[List[str]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        带筛选条件的搜索
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            filters: 筛选条件字典
            top_k: 返回结果数量
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self._ensure_initialized()
        
        try:
            # 处理筛选条件
            filter_expr = FilterExpressionBuilder.build_metadata_filter_expr(filters)
            
            # 执行搜索
            return await self.search_vectors(
                collection_name=collection_name,
                query=query,
                top_k=top_k,
                filter_expr=filter_expr,
                output_fields=output_fields,
                database=database
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="带筛选条件搜索",
                error=e,
                collection=collection_name,
                filters=str(filters)
            )
            raise Exception(f"带筛选条件搜索失败: {str(e)}")
    
    async def search_similar_documents(
        self,
        collection_name: str,
        document_id: Union[str, int],
        top_k: int = 10,
        exclude_self: bool = True,
        output_fields: Optional[List[str]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        根据文档ID搜索相似文档
        
        Args:
            collection_name: 集合名称
            document_id: 文档ID
            top_k: 返回结果数量
            exclude_self: 是否排除自身
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("相似文档搜索")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(database)
                self.search_repo = SearchRepository(db_connection)
            
            # 获取源文档的向量
            source_vector = await self.search_repo.get_vector_by_id(
                collection_name=collection_name,
                vector_id=document_id
            )
            
            if not source_vector:
                raise ValueError(f"未找到ID为 '{document_id}' 的文档")
            
            # 构建筛选条件（排除自身）
            filter_expr = None
            if exclude_self:
                filter_expr = f"id != '{document_id}'"
            
            # 执行向量搜索
            search_results = await self.search_repo.search_vectors(
                collection_name=collection_name,
                query_vector=source_vector['vector'],
                top_k=top_k,
                filter_expr=filter_expr or ""
            )
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="相似文档搜索",
                collection=collection_name,
                document_id=document_id,
                results_count=len(formatted_results),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.search_response(
                query=f"相似文档搜索 (基于文档ID: {document_id})",
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                collection=collection_name,
                database=database
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="相似文档搜索",
                error=e,
                collection=collection_name,
                document_id=document_id
            )
            raise Exception(f"相似文档搜索失败: {str(e)}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "initialized": self._initialized,
            "components": {
                "search_repository": "active" if self.search_repo else "inactive",
                "embedding_service": "active" if self.embedding_service else "inactive",
                "database_repository": "active"
            }
        } 