#!/bin/bash

# ========================================================================================
# 脚本说明:
# 使用Gunicorn启动FastAPI应用，并将其作为后台守护进程运行。
#
# 使用方法:
# 1. 将此脚本放置在您的项目根目录下。
# 2. 根据需要修改下面的配置变量。
# 3. 在终端中运行 `bash scripts/start.sh` 来启动服务。
# ========================================================================================

# --- 可配置的变量 ---

# Gunicorn工作进程数，一个常见的建议是 (2 * CPU核心数) + 1
WORKERS=4

# Gunicorn绑定的地址和端口
BIND_ADDRESS="0.0.0.0:8000"

# FastAPI应用实例的位置，格式为 `模块名:应用实例名`
# 假设您的项目结构是 app/main.py，并且FastAPI实例名为 app
APP_INSTANCE="app.main:app"

# 日志文件名
LOG_FILE="server.log"

# PID文件名，用于存储主进程ID，方便停止服务
PID_FILE="gunicorn.pid"

# --- 启动命令 ---

echo "🚀 正在启动RAG服务..."

# 使用 nohup 将gunicorn挂在后台运行
# > ${LOG_FILE} 2>&1  表示将标准输出和标准错误都重定向到日志文件
# &                  表示在后台执行
# --pid ${PID_FILE}  是gunicorn的参数，它会将主进程ID写入指定文件
nohup gunicorn ${APP_INSTANCE} \
    -w ${WORKERS} \
    -k uvicorn.workers.UvicornWorker \
    -b ${BIND_ADDRESS} \
    --timeout 300 \
    --graceful-timeout 300 \
    --pid ${PID_FILE} \
    > ${LOG_FILE} 2>&1 &

# 等待一小会儿，然后检查进程是否真的启动了
sleep 2

if [ -f $PID_FILE ]; then
    echo "✅ 服务启动成功!"
    echo "   - 工作进程数: ${WORKERS}"
    echo "   - 监听地址: http://${BIND_ADDRESS}"
    echo "   - 日志文件: tail -f ${LOG_FILE}"
    echo "   - 进程ID (PID): $(cat ${PID_FILE})"
else
    echo "❌ 服务启动失败! 请检查 ${LOG_FILE} 文件以获取错误信息。"
fi