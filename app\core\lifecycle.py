"""
应用生命周期管理器
处理应用启动和关闭时的资源管理
"""
import time
import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI

logger = logging.getLogger(__name__)


class LifecycleManager:
    """应用生命周期管理器"""
    
    def __init__(self):
        self.startup_tasks = []
        self.shutdown_tasks = []
    
    def add_startup_task(self, task):
        """添加启动任务"""
        self.startup_tasks.append(task)
    
    def add_shutdown_task(self, task):
        """添加关闭任务"""
        self.shutdown_tasks.append(task)
    
    async def startup(self):
        """执行启动任务"""
        logger.info("=" * 50)
        logger.info("服务正在启动，预加载模型和数据库...")
        start_time = time.time()
        
        # 预热数据库连接池
        await self._warmup_database_pool()
        
        # 并行执行所有预加载任务
        if self.startup_tasks:
            logger.info("开始执行启动任务...")
            await asyncio.gather(*[task() for task in self.startup_tasks])
        
        # 计算总耗时
        total_time = time.time() - start_time
        logger.info(f"所有组件预加载完成，总耗时: {total_time:.2f} 秒")
        logger.info("服务已准备就绪，可以接受请求")
        logger.info("=" * 50)
    
    async def shutdown(self):
        """执行关闭任务"""
        logger.info("服务正在关闭...")
        
        # 执行所有关闭任务
        if self.shutdown_tasks:
            await asyncio.gather(*[task() for task in self.shutdown_tasks])
        
        logger.info("服务已关闭")
    
    async def _warmup_database_pool(self):
        """预热数据库连接池"""
        try:
            logger.info("预热数据库连接池...")
            pool_start = time.time()
            
            # 获取向量数据库配置
            from app.dependencies import get_vectordb_config_sync
            cfg = get_vectordb_config_sync()
            
            # 预热连接池
            from app.core.vectordb import MilvusVectorDB
            # 预热默认数据库连接
            await MilvusVectorDB.get_connection(
                uri=cfg.uri,
                token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None,
                database=None
            )
            
            
            pool_time = time.time() - pool_start
            logger.info(f"数据库连接池预热完成，耗时: {pool_time:.2f}秒")
        except Exception as e:
            logger.error(f"数据库连接池预热失败: {e}")
            logger.warning("将在首次请求时建立连接")


# 全局生命周期管理器实例
lifecycle_manager = LifecycleManager()


def register_startup_task(task):
    """注册启动任务的装饰器"""
    lifecycle_manager.add_startup_task(task)
    return task


def register_shutdown_task(task):
    """注册关闭任务的装饰器"""
    lifecycle_manager.add_shutdown_task(task)
    return task


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI生命周期管理函数"""
    # 启动时执行
    await lifecycle_manager.startup()
    
    yield
    
    # 关闭时执行
    await lifecycle_manager.shutdown()


# 注册默认的启动任务
@register_startup_task
async def load_models_and_database():
    """加载模型和数据库"""
    from app.dependencies import get_embed_model_sync, get_vector_db_sync
    
    tasks = [
        get_embed_model_sync(),
        get_vector_db_sync()
    ]
    
    await asyncio.gather(*tasks) 